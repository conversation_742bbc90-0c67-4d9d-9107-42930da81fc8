<?php

namespace App\Models;

use App\Traits\HasFiltersAndSorts;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Laravel\Scout\Searchable;
use Spatie\MediaLibrary\HasMedia;
use Spa<PERSON>\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Product extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia, HasFiltersAndSorts, Searchable;

    protected $fillable = [
        'sku',
        'molos_id',
        'name',
        'slug',
        'description',
        'short_description',
        'generated_description',
        'price',
        'price_percentage',
        'molos_price',
        'stock',
        'image',
        'weight',
        'length',
        'width',
        'height',
        'category_id',
        'producent_id',
        'gallery',
        'ean',
        'status',
        'unit',
        'molos_category_id',
        'vat',
    ];

    protected $appends = ['discounted_price', 'url'];

    protected $casts = [
        'price' => 'decimal:2',
    ];

    protected static $filters = [
        'name' => ['type' => 'text'],
        'category_id' => ['type' => 'relation', 'relation' => 'category'],
        'price' => ['type' => 'numberrange'],
        'created_at' => ['type' => 'daterange'],
    ];

    protected static $sorts = [
        'name',
        'ean',
        'price',
        'vat',
    ];

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        $array = $this->toArray();

        // Customize the data array as needed
        return [
            'id' => $this->id,
            'sku' => $this->sku,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'price' => $this->price,
            'category' => $this->category ? $this->category->name : null,
            'ean' => $this->ean,
            // Add other fields as necessary
        ];
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function producent()
    {
        return $this->belongsTo(Producent::class);
    }
    
    public function priceHistories()
    {
        return $this->hasMany(PriceHistory::class);
    }

    public function properties()
    {
        return $this->belongsToMany(Property::class, 'product_property')
                    ->withPivot('value', 'property_option_id');
    }

    public function discounts()
    {
        return $this->belongsToMany(Discount::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function currentDiscount()
    {
        return $this->discounts()
                    // ->where('start_date', '<=', now())
                    // ->where(function ($query) {
                    //     $query->where('end_date', '>=', now())
                    //         ->orWhereNull('end_date');
                    // })
                    ->orderByDesc('percentage')
                    ->first();
    }

    public function currentPrice()
    {
        return $this->price;
    }

    public function getMinPriceLast30Days()
    {
        $thirtyDaysAgo = now()->subDays(30);
        $minPrice = $this->priceHistories()
            ->where('date', '>=', $thirtyDaysAgo)
            ->min('price');

        if ($minPrice === null) {
            return $this->priceHistories()->oldest()->value('price') ?? $this->price;
        }

        return $minPrice;
    }

    /**
     * Images
     */
    public function registerMediaConversions(Media $media = null): void
    {
        
        $this->addMediaConversion('thumb')
              ->width(220)
              ->height(220)
              ->sharpen(10);

        $this->addMediaConversion('full')
              ->width(1000)
              ->height(1000)
              ->sharpen(10);

        $this->addMediaConversion('cart')
              ->width(50)
              ->height(50)
              ->sharpen(10);
    }

    public function getImagesAttribute()
    {
        $returnImages = [];
        foreach ($this->getMedia('images') as $image)
        {
            $returnImages[] = $image->getUrl();
        }
        return $returnImages;
    }

    public function getImageSrcAttribute()
    {
        $mediaItems = $this->getMedia('images');
        if (isset($mediaItems[0]))
        {
            return $mediaItems[0]->getUrl();
        }
        return '/img/nophoto.jpg';
    }

    public function getThumbAttribute()
    {
        $mediaItems = $this->getMedia('images');
        if (isset($mediaItems[0]))
        {
            if ($mediaItems[0]->hasGeneratedConversion('thumb'))
            {
                return $mediaItems[0]->getUrl('thumb');
            }
            else
            {
                return $mediaItems[0]->getUrl();
            }
        }
        return '/img/nophoto.jpg';
    }

    /**
     * Get image for cart
     */
    public function getCartImgAttribute()
    {
        $mediaItems = $this->getMedia('images');
        if (isset($mediaItems[0]))
        {
            if ($mediaItems[0]->hasGeneratedConversion('cart'))
            {
                return $mediaItems[0]->getUrl('cart');
            }
            else
            {
                return $mediaItems[0]->getUrl();
            }
        }
        return 'https://zoo-mall.pl/img/nophoto.jpg';
    }

    /**
     * Scopes
     */
    public function scopeInStock($query)
    {
        return $query->with('media')
            ->where('stock', '>=', 1)
            ->where('category_id', '>', 0)
            ->whereHas('category', function ($q) {
                $q->where('is_visible', true);
            });
    }

    /**
     * Mutations
     */
    public function getDiscountedPriceAttribute()
    {

        $discount = $this->currentDiscount();

        if ($discount) {
            return number_format($this->price * (100 - $discount->percentage) / 100, 2);
        }

        return $this->price;
    }

    /**
     * Get the "in_cart" attribute for the product.
     *
     * @return bool
     */
    public function getInCartAttribute()
    {
        $cartService = app(\App\Services\App\CartService::class);
        return $cartService->isProductInCart($this);
    }

    /**
     * Get the cart item associated with the product.
     *
     * @return \App\Models\CartItem|null
     */
    public function getCartItemAttribute()
    {
        $cartService = app(\App\Services\App\CartService::class);
        return $cartService->getCartItemByProduct($this);
    }

    public function getUrlAttribute()
    {
        if (!$this->category)
        {
            return '/';
        }
        return route('detail', ['path' => $this->category->url, 'product' => $this]);
    }

    public function molosCategory()
    {
        return $this->belongsTo(MolosCategory::class, 'molos_category_id', 'molos_id');
    }

    /**
     * Get the price percentage for this product
     * Priority: Product percentage > Category percentage > Default 25
     */
    public function getPricePercentage()
    {
        // Check if product has its own percentage
        if ($this->price_percentage !== null) {
            return $this->price_percentage;
        }

        // Check if category has percentage
        if ($this->category && $this->category->price_percentage !== null) {
            return $this->category->price_percentage;
        }

        // Default percentage
        return 25;
    }

    /**
     * Calculate price based on Molos price and percentage
     */
    public function calculatePriceFromMolos()
    {
        if (!$this->molos_price) {
            return $this->price;
        }

        $percentage = $this->getPricePercentage();
        return round($this->molos_price * (1 + ($percentage / 100)), 2);
    }

    /**
     * Update price based on current percentage settings
     */
    public function updatePriceFromPercentage()
    {
        if ($this->molos_price) {
            $this->price = $this->calculatePriceFromMolos();
            $this->save();
        }
    }

    /**
     * Check if product has dimensions
     */
    public function hasDimensions()
    {
        return !is_null($this->length) && !is_null($this->width) && !is_null($this->height);
    }

    /**
     * Get product volume in cubic centimeters
     */
    public function getVolume()
    {
        if (!$this->hasDimensions()) {
            return 0;
        }
        return $this->length * $this->width * $this->height;
    }

    /**
     * Get dimensions as array
     */
    public function getDimensions()
    {
        return [
            'length' => $this->length ?? 0,
            'width' => $this->width ?? 0,
            'height' => $this->height ?? 0,
            'weight' => $this->weight ?? 0,
        ];
    }

    /**
     * Check if dimensions are reasonable (not too small or too large)
     */
    public function hasReasonableDimensions()
    {
        if (!$this->hasDimensions()) {
            return false;
        }
        
        // Check if dimensions are within reasonable bounds (0.5cm to 200cm)
        $dimensions = [$this->length, $this->width, $this->height];
        foreach ($dimensions as $dimension) {
            if ($dimension < 0.5 || $dimension > 200) {
                return false;
            }
        }
        
        return true;
    }
}
